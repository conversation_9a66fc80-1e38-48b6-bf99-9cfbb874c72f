#!/usr/bin/env python3
"""
Test the fixed SM2 functions
"""
from utils.test import encrypt_c1c3c222, encrypt_c1c3c2

def test_both_functions():
    """Test both encryption functions"""
    data = "Hello from Python"
    public_key = "04BAE7ADE4862DB210BE1715C09D380218F6D4D32CA8CDBF50355C2A2D87A3191601CCA45626608499C7DEACE9D20D22ADE7CB6F255BA0B3B97520B17AD99F72DC"
    
    print("=== Testing Fixed SM2 Functions ===")
    print(f"Test data: {data}")
    print(f"Public key: {public_key[:50]}...")
    
    # Test the fixed function
    print("\n1. Testing encrypt_c1c3c222 (fixed function):")
    try:
        result1 = encrypt_c1c3c222(data.encode('utf-8'), public_key)
        print(f"✅ Success! Result length: {len(result1)} bytes")
        print(f"Hex result: {result1.hex()[:100]}...")
    except Exception as e:
        print(f"❌ Failed: {e}")
    
    # Test the working function
    print("\n2. Testing encrypt_c1c3c2 (working function):")
    try:
        result2 = encrypt_c1c3c2(data.encode('utf-8'), public_key)
        print(f"✅ Success! Result length: {len(result2)} bytes")
        print(f"Hex result: {result2.hex()[:100]}...")
    except Exception as e:
        print(f"❌ Failed: {e}")
    
    # Compare results
    print("\n3. Comparing results:")
    try:
        if 'result1' in locals() and 'result2' in locals():
            if result1 == result2:
                print("✅ Both functions produce identical results")
            else:
                print("ℹ️ Functions produce different results (expected due to randomness)")
                print(f"Function 1 length: {len(result1)}")
                print(f"Function 2 length: {len(result2)}")
        else:
            print("⚠️ Cannot compare - one or both functions failed")
    except Exception as e:
        print(f"❌ Comparison failed: {e}")

if __name__ == "__main__":
    test_both_functions()
