"""
SM2加密工具类
参考Java SM2Utils实现，使用gmssl库实现SM2加密功能，输出BCD格式以匹配API服务器

国密非对称加密SM2工具类
参考: com.chinaunicom.datagov.util.SM2Utils
"""
import base64
import logging
from typing import Optional

from pyasn1.codec.der import decoder
from pyasn1.type import univ
from gmssl import sm2

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from gmssl import sm2
    GMSSL_AVAILABLE = True
    logger.info("✅ gmssl库加载成功")
except ImportError:
    GMSSL_AVAILABLE = False
    logger.warning("⚠️ 警告: gmssl库未正确安装，将使用模拟加密")

class SM2Utils:
    """SM2加密工具类，参考Java SM2Utils.java实现"""
    def extract_raw_pubkey_from_x509_der(der_data):
        spki, _ = decoder.decode(der_data, asn1Spec=univ.Sequence())
        pubkey_bitstring = spki[1]
        pubkey_bytes = pubkey_bitstring.asOctets()
        return pubkey_bytes.hex().upper()

    def encrypt_by_public_key(data: str, public_key_b64: str) -> str:
        # Step 1: Base64 解码 DER 数据
        der_data = base64.b64decode(public_key_b64.strip())

        # Step 2: 提取原始 SM2 公钥 Hex（x + y）
        raw_pubkey_hex = SM2Utils.extract_raw_pubkey_from_x509_der(der_data)

        # Step 3: 使用 gmssl 加密
        cipher = sm2.CryptSM2(public_key=raw_pubkey_hex, private_key="")
        cipher_bytes = cipher.encrypt(data.encode('utf-8'))
        # Step 4: 返回 Hex 编码结果（与 Hutool 的 encryptBcd 一致）
        return encrypt_c1c3c2(cipher_bytes).upper()

    from gmssl import sm2

def encrypt_c1c3c2(cipher_c1c2c3) -> str:

    # Step 2: 手动解析 C1C2C3 结构
    c1 = cipher_c1c2c3[:65]         # C1 是椭圆曲线点（65 字节）
    c3 = cipher_c1c2c3[-32:]        # C3 是摘要（32 字节）
    c2 = cipher_c1c2c3[65:-32]      # C2 是数据密文

    # Step 3: 拼接为 C1C3C2 格式
    cipher_c1c3c2 = c1 + c3 + c2

    # 返回 Hex 编码（大写）
    return cipher_c1c3c2.hex().upper()