# snowland-smx SM2加密实现文档

## 📋 概述

本文档描述了如何使用snowland-smx库实现SM2公钥加密功能，以替代或补充现有的gmssl实现。

## 🎯 实现目标

- ✅ 使用snowland-smx库实现SM2加密
- ✅ 兼容现有的X.509 DER格式公钥
- ✅ 输出与Java Hutool encryptBcd格式一致的大写十六进制
- ✅ 提供与现有代码兼容的接口
- ✅ 完整的测试验证

## 📦 安装依赖

### 自动安装
```bash
pip install snowland-smx>=0.3.1 pyasn1>=0.4.8
```

### 或使用requirements.txt
```bash
pip install -r requirements.txt
```

## 🔧 核心实现

### 主要文件

1. **`utils/sm2_snowland.py`** - snowland-smx SM2加密工具类
2. **`test_snowland_sm2.py`** - 综合测试文件
3. **`example_snowland_sm2.py`** - 使用示例

### 核心类：SM2UtilsSnowland

```python
from utils.sm2_snowland import SM2UtilsSnowland

# 加密数据
result = SM2UtilsSnowland.encrypt_by_public_key(data, public_key_base64)
```

## 🚀 使用方法

### 方法1：使用类方法（推荐）

```python
from utils.sm2_snowland import SM2UtilsSnowland

# 你的公钥（Base64编码的X.509 DER格式）
public_key = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE..."

# 要加密的数据
data = '{"user":"test", "message":"Hello SM2!"}'

# 加密
encrypted_result = SM2UtilsSnowland.encrypt_by_public_key(data, public_key)

if encrypted_result:
    print(f"加密成功: {encrypted_result}")
else:
    print("加密失败")
```

### 方法2：使用兼容函数

```python
from utils.sm2_snowland import encrypt_by_public_key

# 直接调用兼容函数
encrypted_result = encrypt_by_public_key(data, public_key)
```

## 🔍 特性说明

### 输入格式
- **数据**: UTF-8编码的字符串
- **公钥**: Base64编码的X.509 DER格式

### 输出格式
- **成功**: 大写十六进制字符串（与Java Hutool encryptBcd一致）
- **失败**: None

### 加密特性
- ✅ 使用SM2国密算法
- ✅ 每次加密结果不同（包含随机数）
- ✅ 与Java实现兼容
- ✅ 支持中文和特殊字符

## 🧪 测试验证

### 运行综合测试
```bash
python test_snowland_sm2.py
```

### 运行使用示例
```bash
python example_snowland_sm2.py
```

### 测试结果
- ✅ 与现有公钥兼容性测试通过
- ✅ 加密随机性验证通过
- ✅ 兼容函数测试通过
- ✅ 与gmssl实现对比测试通过

## 📊 性能对比

| 特性 | snowland-smx | gmssl |
|------|-------------|-------|
| 安装简便性 | ✅ 简单 | ✅ 简单 |
| 依赖数量 | ✅ 较少 | ⚠️ 较多 |
| 文档完整性 | ✅ 完整 | ⚠️ 一般 |
| 社区活跃度 | ⚠️ 一般 | ✅ 活跃 |
| 加密结果 | ✅ 兼容 | ✅ 兼容 |

## 🔄 与现有代码集成

### 替换现有实现
如果要完全替换gmssl实现，可以修改现有的`utils/sm2_utils.py`：

```python
# 在sm2_utils.py中添加
from .sm2_snowland import SM2UtilsSnowland

class SM2Utils:
    @staticmethod
    def encrypt_by_public_key(data: str, public_key_str: str) -> str:
        # 使用snowland-smx实现
        return SM2UtilsSnowland.encrypt_by_public_key(data, public_key_str)
```

### 并行使用
保持两种实现并存，根据需要选择：

```python
from utils.sm2_utils import SM2Utils as SM2UtilsGmssl
from utils.sm2_snowland import SM2UtilsSnowland

# 使用gmssl
result1 = SM2UtilsGmssl.encrypt_by_public_key(data, public_key)

# 使用snowland-smx
result2 = SM2UtilsSnowland.encrypt_by_public_key(data, public_key)
```

## ⚠️ 注意事项

1. **随机性**: SM2加密包含随机数，每次加密结果都不同，这是正常现象
2. **公钥格式**: 确保公钥是Base64编码的X.509 DER格式
3. **错误处理**: 加密失败时返回None，需要进行适当的错误处理
4. **字符编码**: 输入数据使用UTF-8编码

## 🐛 故障排除

### 常见问题

1. **ImportError: No module named 'pysmx'**
   ```bash
   pip install snowland-smx
   ```

2. **加密返回None**
   - 检查公钥格式是否正确
   - 确认snowland-smx库已正确安装
   - 查看日志输出获取详细错误信息

3. **结果格式不正确**
   - 确认使用的是SM2UtilsSnowland类
   - 检查返回值是否为None

## 📝 更新日志

- **v1.0.0** (2024-12-01)
  - ✅ 初始实现snowland-smx SM2加密
  - ✅ 完整的测试套件
  - ✅ 与现有代码兼容
  - ✅ 文档和示例

## 📞 支持

如有问题，请检查：
1. 依赖是否正确安装
2. 公钥格式是否正确
3. 运行测试文件验证功能
4. 查看日志输出获取详细信息
