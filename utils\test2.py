import base64
from pyexpat import model
from pyasn1.codec.der import decoder
from pyasn1.type import univ
from gmssl import sm2

def extract_raw_pubkey_from_x509_der(der_data):
    spki, _ = decoder.decode(der_data, asn1Spec=univ.Sequence())
    pubkey_bitstring = spki[1]
    pubkey_bytes = pubkey_bitstring.asOctets()
    return pubkey_bytes.hex().upper()

def encrypt_c1c3c2(public_key_hex: str, data: bytes) -> str:
    crypt = sm2.CryptSM2(public_key= public_key_hex, private_key="", mode=1, asn1= True)
    cipher_c1c2c3 = crypt.encrypt(data)
 
    return cipher_c1c2c3.hex().upper()

# 示例调用
if __name__ == '__main__':
    public_key_b64 = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEuuet5IYtshC+FxXAnTgCGPbU0yyozb9QNVwqLYejGRYBzKRWJmCEmcferOnSDSKt58tvJVugs7l1ILF62Z9y3A=="
    data = "Hello from Java"

    # Step 1: Base64 解码 DER 数据
    der_data = base64.b64decode(public_key_b64.strip())

    # Step 2: 提取原始公钥 Hex（x+y）
    raw_pubkey_hex = extract_raw_pubkey_from_x509_der(der_data)

    # Step 3: 加密为 C1C3C2 格式
    encrypted_hex = encrypt_c1c3c2(raw_pubkey_hex, data.encode('utf-8'))

    print("Encrypted (Hex):", encrypted_hex)