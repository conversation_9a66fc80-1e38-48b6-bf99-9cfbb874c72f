from gmssl import sm2
from utils.sm2_utils import SM2Utils

def encrypt_c1c3c222(data: bytes, public_key_hex: str) -> bytes:
    """
    使用 SM2 公钥加密数据，返回 C1C3C2 格式密文
    这是一个修复后的版本，使用正确的 gmssl API
    """
    # 直接使用 gmssl 库的标准加密方法，它已经实现了 C1C3C2 格式
    sm2_cipher = sm2.CryptSM2(public_key=public_key_hex, private_key="")

    # gmssl 的 encrypt 方法已经返回 C1C3C2 格式的密文
    encrypted_data = sm2_cipher.encrypt(data)

    if encrypted_data is None:
        raise ValueError("Encryption failed")

    return encrypted_data





def encrypt_data(data: bytes, public_key_hex: str) -> bytes:
    """
    使用 SM2 公钥加密数据，并返回 C1C3C2 格式密文（与 Hutool 一致）
    """
    # 使用 gmssl 库的标准加密方法
    sm2_cipher = sm2.CryptSM2(public_key=public_key_hex, private_key="")

    # 直接使用 gmssl 的加密功能
    encrypted_data = sm2_cipher.encrypt(data)

    if encrypted_data is None:
        raise ValueError("Encryption failed")

    return encrypted_data
def encrypt_c1c3c2(cipher_c1c2c3) -> str:

    # Step 2: 手动解析 C1C2C3 结构
    c1 = cipher_c1c2c3[:65]         # C1 是椭圆曲线点（65 字节）
    c3 = cipher_c1c2c3[-32:]        # C3 是摘要（32 字节）
    c2 = cipher_c1c2c3[65:-32]      # C2 是数据密文

    # Step 3: 拼接为 C1C3C2 格式
    cipher_c1c3c2 = c1 + c3 + c2

    # 返回 Hex 编码（大写）
    return cipher_c1c3c2.hex().upper()


publickey = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEuuet5IYtshC+FxXAnTgCGPbU0yyozb9QNVwqLYejGRYBzKRWJmCEmcferOnSDSKt58tvJVugs7l1ILF62Z9y3A=="

data = "Hello from Java"


 
print(SM2Utils.encrypt_by_public_key(data, publickey))


