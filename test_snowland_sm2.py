#!/usr/bin/env python3
"""
测试snowland-smx SM2加密功能
与现有的gmssl实现进行对比测试
"""
import sys
import os

# 添加utils目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

from utils.sm2_snowland import SM2UtilsSnowland, encrypt_by_public_key
from utils.sm2_utils import SM2Utils as SM2UtilsGmssl

def test_snowland_with_existing_key():
    """使用现有的公钥测试snowland-smx加密"""
    print("=== 测试snowland-smx与现有公钥的兼容性 ===")
    
    # 使用你现有的测试公钥
    public_key_base64 = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEuuet5IYtshC+FxXAnTgCGPbU0yyozb9QNVwqLYejGRYBzKRWJmCEmcferOnSDSKt58tvJVugs7l1ILF62Z9y3A=="
    
    # 测试数据
    test_data = '{"user":"shs-xxaqbzylhy", "name":"许国杰", "orgCode":"数据处室", "orgName":"数据处室", "uuid":"test-uuid", "day":"20241201"}'
    
    print(f"公钥: {public_key_base64[:50]}...")
    print(f"测试数据: {test_data}")
    
    # 测试snowland-smx加密
    print("\n1. 测试snowland-smx加密...")
    snowland_result = SM2UtilsSnowland.encrypt_by_public_key(test_data, public_key_base64)
    
    if snowland_result:
        print(f"✅ snowland-smx加密成功!")
        print(f"加密结果长度: {len(snowland_result)}")
        print(f"加密结果: {snowland_result[:100]}...")
        
        # 验证结果格式
        if snowland_result.isupper() and all(c in '0123456789ABCDEF' for c in snowland_result):
            print("✅ 结果格式正确：大写十六进制")
        else:
            print("⚠️ 结果格式可能不正确")
    else:
        print("❌ snowland-smx加密失败")
        return False
    
    # 测试gmssl加密进行对比
    print("\n2. 测试gmssl加密（对比）...")
    try:
        gmssl_result = SM2UtilsGmssl.encrypt_by_public_key(test_data, public_key_base64)
        
        if gmssl_result:
            print(f"✅ gmssl加密成功!")
            print(f"加密结果长度: {len(gmssl_result)}")
            print(f"加密结果: {gmssl_result[:100]}...")
        else:
            print("❌ gmssl加密失败")
    except Exception as e:
        print(f"❌ gmssl加密异常: {e}")
    
    # 比较结果
    print("\n3. 结果比较...")
    if snowland_result and gmssl_result:
        print(f"snowland-smx长度: {len(snowland_result)}")
        print(f"gmssl长度: {len(gmssl_result)}")
        
        if len(snowland_result) == len(gmssl_result):
            print("✅ 两种实现的加密结果长度一致")
        else:
            print("⚠️ 两种实现的加密结果长度不同")
        
        # 由于SM2加密包含随机数，每次结果都不同，这是正常的
        print("ℹ️ 注意：SM2加密包含随机数，每次结果都不同是正常现象")
    
    return True

def test_multiple_encryptions():
    """测试多次加密的随机性"""
    print("\n=== 测试加密随机性 ===")
    
    public_key_base64 = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEuuet5IYtshC+FxXAnTgCGPbU0yyozb9QNVwqLYejGRYBzKRWJmCEmcferOnSDSKt58tvJVugs7l1ILF62Z9y3A=="
    test_data = "Hello SM2 Encryption Test"
    
    print(f"测试数据: {test_data}")
    print("进行3次加密，验证结果的随机性...")
    
    results = []
    for i in range(3):
        result = SM2UtilsSnowland.encrypt_by_public_key(test_data, public_key_base64)
        if result:
            results.append(result)
            print(f"加密 {i+1}: {result[:50]}...")
        else:
            print(f"❌ 加密 {i+1} 失败")
            return False
    
    # 检查结果是否都不同（应该不同，因为包含随机数）
    if len(set(results)) == len(results):
        print("✅ 所有加密结果都不同（正常，因为包含随机数）")
    else:
        print("⚠️ 有重复的加密结果（不太正常）")
    
    return True

def test_compatibility_functions():
    """测试兼容性函数"""
    print("\n=== 测试兼容性函数 ===")
    
    public_key_base64 = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEuuet5IYtshC+FxXAnTgCGPbU0yyozb9QNVwqLYejGRYBzKRWJmCEmcferOnSDSKt58tvJVugs7l1ILF62Z9y3A=="
    test_data = "Test compatibility functions"
    
    print("测试兼容性函数 encrypt_by_public_key...")
    result = encrypt_by_public_key(test_data, public_key_base64)
    
    if result:
        print(f"✅ 兼容性函数工作正常")
        print(f"结果: {result[:50]}...")
        return True
    else:
        print("❌ 兼容性函数失败")
        return False

def main():
    """主测试函数"""
    print("🚀 snowland-smx SM2加密测试")
    print("=" * 50)
    
    success_count = 0
    total_tests = 3
    
    # 测试1：与现有公钥的兼容性
    if test_snowland_with_existing_key():
        success_count += 1
    
    # 测试2：多次加密的随机性
    if test_multiple_encryptions():
        success_count += 1
    
    # 测试3：兼容性函数
    if test_compatibility_functions():
        success_count += 1
    
    # 总结
    print("\n" + "=" * 50)
    print(f"测试完成: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！snowland-smx SM2加密工作正常")
        print("\n📋 使用说明:")
        print("1. 导入: from utils.sm2_snowland import SM2UtilsSnowland")
        print("2. 加密: result = SM2UtilsSnowland.encrypt_by_public_key(data, public_key)")
        print("3. 或使用兼容函数: from utils.sm2_snowland import encrypt_by_public_key")
    else:
        print("❌ 部分测试失败，请检查配置")
    
    return success_count == total_tests

if __name__ == "__main__":
    main()
