#!/usr/bin/env python3
"""
snowland-smx SM2加密使用示例
演示如何使用snowland-smx库进行SM2公钥加密
"""

from utils.sm2_snowland import SM2UtilsSnowland, encrypt_by_public_key

def main():
    """主函数：演示snowland-smx SM2加密的使用"""
    print("🔐 snowland-smx SM2加密使用示例")
    print("=" * 50)
    
    # 你的公钥（Base64编码的X.509 DER格式）
    public_key_base64 = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEuuet5IYtshC+FxXAnTgCGPbU0yyozb9QNVwqLYejGRYBzKRWJmCEmcferOnSDSKt58tvJVugs7l1ILF62Z9y3A=="
    
    # 要加密的数据
    data_to_encrypt = '{"user":"test-user", "message":"Hello SM2 with snowland-smx!", "timestamp":"2024-12-01"}'
    
    print(f"📝 要加密的数据:")
    print(f"   {data_to_encrypt}")
    print(f"\n🔑 公钥 (前50字符):")
    print(f"   {public_key_base64[:50]}...")
    
    # 方法1：使用类方法
    print(f"\n🔧 方法1：使用 SM2UtilsSnowland.encrypt_by_public_key()")
    encrypted_result1 = SM2UtilsSnowland.encrypt_by_public_key(data_to_encrypt, public_key_base64)
    
    if encrypted_result1:
        print(f"✅ 加密成功!")
        print(f"   结果长度: {len(encrypted_result1)} 字符")
        print(f"   加密结果: {encrypted_result1[:80]}...")
        print(f"   格式验证: {'✅ 大写十六进制' if encrypted_result1.isupper() and all(c in '0123456789ABCDEF' for c in encrypted_result1) else '❌ 格式错误'}")
    else:
        print("❌ 加密失败")
        return
    
    # 方法2：使用兼容函数
    print(f"\n🔧 方法2：使用兼容函数 encrypt_by_public_key()")
    encrypted_result2 = encrypt_by_public_key(data_to_encrypt, public_key_base64)
    
    if encrypted_result2:
        print(f"✅ 加密成功!")
        print(f"   结果长度: {len(encrypted_result2)} 字符")
        print(f"   加密结果: {encrypted_result2[:80]}...")
    else:
        print("❌ 加密失败")
        return
    
    # 验证随机性
    print(f"\n🎲 验证加密随机性:")
    print(f"   两次加密结果是否不同: {'✅ 是' if encrypted_result1 != encrypted_result2 else '❌ 否'}")
    print(f"   (SM2加密包含随机数，每次结果都应该不同)")
    
    # 与Java兼容性说明
    print(f"\n🔗 与Java代码兼容性:")
    print(f"   ✅ 输出格式: 大写十六进制 (与Java Hutool encryptBcd一致)")
    print(f"   ✅ 公钥格式: 支持X.509 DER Base64编码")
    print(f"   ✅ 加密算法: SM2国密算法")
    
    # 使用建议
    print(f"\n💡 使用建议:")
    print(f"   1. 在你的应用中导入: from utils.sm2_snowland import SM2UtilsSnowland")
    print(f"   2. 调用加密: result = SM2UtilsSnowland.encrypt_by_public_key(data, public_key)")
    print(f"   3. 检查结果: if result: # 加密成功，result是大写十六进制字符串")
    print(f"   4. 错误处理: 如果返回None，表示加密失败")
    
    print(f"\n🎉 示例完成！snowland-smx SM2加密工作正常")

if __name__ == "__main__":
    main()
