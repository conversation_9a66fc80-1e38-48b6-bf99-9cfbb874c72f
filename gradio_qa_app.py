"""
智能问答登录测试 Gradio 应用
基于 TestQARequest.java 功能实现
"""
import gradio as gr
import requests
import json
import uuid
from datetime import datetime
from typing import Tuple, Optional
from utils.sm2_utils import SM2Utils
from utils.login_response import LoginResponse
from utils.sm2_snowland import SM2UtilsSnowland


class QALoginTester:
    """智能问答登录测试器"""
    
    def __init__(self):
        self.base_url = "http://10.8.252.60:3900/api-gateway"
        self.endpoint = "/touchSso/ssoLogin"
    
    def generate_uuid(self) -> str:
        """生成UUID"""
        return str(uuid.uuid4())
    
    def get_current_date(self) -> str:
        """获取当前日期 (yyyyMMdd格式)"""
        return datetime.now().strftime("%Y%m%d")
    
    def create_user_data(self, user: str, uuid_str: str, day: str) -> str:
        """创建用户数据JSON"""
        data = {
            "user": user,
            "name": "许国杰",
            "orgCode": "数据处室",
            "orgName": "数据处室", 
            "uuid": uuid_str,
            "day": day
        }
        return json.dumps(data, ensure_ascii=False)
    
    def test_login(self, api_key: str, user: str, app_id: str, touch_id: str) -> Tuple[str, str, str]:
        """
        测试登录功能
        
        Returns:
            Tuple[加密数据, 请求URL, 响应结果]
        """
        try:
            # 生成UUID和日期
            uuid_str = self.generate_uuid()
            day = self.get_current_date()
            
            # 创建用户数据
            user_data = self.create_user_data(user, uuid_str, day)
            ## 04616D7F426EA4F8091528AF5DA868649992E8AA5658566EEE5E9EE4D4748F604F1D6F9A6560946EF79BE0B136EDA3BB0F5B6DA3F2C652C25D0CA1AFC7DDBB34F76B299E740701EB333018DE93B5FD34AA0A27C909BDBC7FA51DAC5F36921DE96B2D749559432EBC10A3A02C28854DCAA177C4D3C2918ED699E015FB118A91CEC87AD75CACF15617EA3D6C75FCE54EBC00FC08B36B08EEA089AD8BC41B6A2E48DFFA86C2AD47E784EB327A3DA24E79AA63B67322FB6CCA15505CAA80705431D2349CF69BB4C901F8A00BD0DBF2094942E46C15F5086A63D740C33A66B2B1F6AC6A2044377E4BC122BBC7D2C4669BEA3ABF3989F1AA59144CBF4C99F51287F71B75F23EFD
            ## 0453CA3B9311D0E32B162A231C10705C995B683905EEA9FBB9F9621AAF5ECF7F10C7FB3CE00DA8CE1A5EFDA3F56D6BB33FEEA7BCA68BC301AE2DD92A11A3A69CF3E832D3A2DFB1F89EC5D686D458DCBBC24CB784A74D201930F7F7BF53C0AB3FB6DDE9E5B2ECF13835462DF167273AA62E5714BC74CB8193396D8D56C3469B26B537B0F54B77F62A551E6A75CE7363AC8364943F0E60E20D8B6FBC51170F89D0BAB82C4C324F5659A9887F37237C2D18742F0BC18C3430615A895E8C735F526C321D85045F7753D5401E51C25E1348895C1D23185908093C9C33DC6EFDF7B55EE7DB9B72CF00ABA51F324BA9F87DBCEF73C4B95C81EECB935C177F1B584F19A69C0CA880
            # SM2加密
            encrypted_data = SM2UtilsSnowland.encrypt_by_public_key(user_data, api_key)
            if not encrypted_data:
                return "", "", "❌ SM2加密失败，请检查API密钥格式"
            
            # 构建请求URL
            url = f"{self.base_url}{self.endpoint}?touchId={touch_id}&params={encrypted_data}"
            
            # 发送HTTP请求
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            # 解析响应
            response_data = response.json()
            login_response = LoginResponse.from_dict(response_data)
            
            return encrypted_data, url, login_response.to_formatted_string()
            
        except requests.exceptions.RequestException as e:
            return encrypted_data if 'encrypted_data' in locals() else "", \
                   url if 'url' in locals() else "", \
                   f"❌ 网络请求失败: {str(e)}"
        except json.JSONDecodeError as e:
            return encrypted_data if 'encrypted_data' in locals() else "", \
                   url if 'url' in locals() else "", \
                   f"❌ 响应解析失败: {str(e)}"
        except Exception as e:
            return encrypted_data if 'encrypted_data' in locals() else "", \
                   url if 'url' in locals() else "", \
                   f"❌ 未知错误: {str(e)}"

def create_gradio_interface():
    """创建Gradio界面"""
    tester = QALoginTester()
    
    # 默认值（来自Java代码）
    default_api_key = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEuuet5IYtshC+FxXAnTgCGPbU0yyozb9QNVwqLYejGRYBzKRWJmCEmcferOnSDSKt58tvJVugs7l1ILF62Z9y3A=="
    default_user = "shs-xxaqbzylhy"
    default_app_id = "1745223746865"
    default_touch_id = "1745223746865"
    
    with gr.Blocks(title="智能问答登录测试", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🤖 智能问答登录测试工具")
        gr.Markdown("基于 TestQARequest.java 实现的登录功能测试")
        
        with gr.Row():
            with gr.Column(scale=2):
                gr.Markdown("### 📝 输入参数")
                
                api_key_input = gr.Textbox(
                    label="API密钥",
                    value=default_api_key,
                    placeholder="请输入SM2公钥",
                    lines=3
                )
                
                user_input = gr.Textbox(
                    label="用户名",
                    value=default_user,
                    placeholder="请输入用户名"
                )
                
                with gr.Row():
                    app_id_input = gr.Textbox(
                        label="应用ID",
                        value=default_app_id,
                        placeholder="请输入应用ID"
                    )
                    
                    touch_id_input = gr.Textbox(
                        label="Touch ID",
                        value=default_touch_id,
                        placeholder="请输入Touch ID"
                    )
                
                test_button = gr.Button("🚀 开始测试", variant="primary", size="lg")
            
            with gr.Column(scale=3):
                gr.Markdown("### 📊 测试结果")
                
                with gr.Tabs():
                    with gr.TabItem("响应结果"):
                        result_output = gr.Textbox(
                            label="登录响应",
                            lines=10,
                            placeholder="点击'开始测试'查看结果..."
                        )
                    
                    with gr.TabItem("加密数据"):
                        encrypted_output = gr.Textbox(
                            label="SM2加密后的数据",
                            lines=5,
                            placeholder="加密数据将在这里显示..."
                        )
                    
                    with gr.TabItem("请求URL"):
                        url_output = gr.Textbox(
                            label="完整请求URL",
                            lines=3,
                            placeholder="请求URL将在这里显示..."
                        )
        
        # 绑定事件
        test_button.click(
            fn=tester.test_login,
            inputs=[api_key_input, user_input, app_id_input, touch_id_input],
            outputs=[encrypted_output, url_output, result_output]
        )
        
        gr.Markdown("""
        ### 📋 使用说明
        1. **API密钥**: SM2公钥，用于加密用户数据
        2. **用户名**: 登录用户标识
        3. **应用ID**: 应用程序标识符
        4. **Touch ID**: 触摸登录标识符
        
        ### 🔧 功能特性
        - ✅ 自动生成UUID和当前日期
        - ✅ SM2加密用户数据
        - ✅ HTTP请求发送和响应处理
        - ✅ 详细的错误信息显示
        """)
    
    return demo

if __name__ == "__main__":
    # 创建并启动Gradio应用
    demo = create_gradio_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )
