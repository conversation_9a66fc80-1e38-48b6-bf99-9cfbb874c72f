"""
SM2加密工具类 - 使用snowland-smx库实现
参考Java SM2Utils实现，使用snowland-smx库实现SM2加密功能，输出BCD格式以匹配API服务器

国密非对称加密SM2工具类
参考: com.chinaunicom.datagov.util.SM2Utils
使用snowland-smx库替代gmssl
"""
import base64
import logging
from typing import Optional

from pyasn1.codec.der import decoder
from pyasn1.type import univ

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from pysmx.SM2 import Encrypt, generate_keypair
    SNOWLAND_SMX_AVAILABLE = True
    logger.info("✅ snowland-smx库加载成功")
except ImportError:
    SNOWLAND_SMX_AVAILABLE = False
    logger.warning("⚠️ 警告: snowland-smx库未正确安装，将使用模拟加密")


def extract_raw_pubkey_from_x509_der(der_data):
    """
    从X.509 DER格式中提取原始SM2公钥
    """
    spki, _ = decoder.decode(der_data, asn1Spec=univ.Sequence())
    pubkey_bitstring = spki[1]
    pubkey_bytes = pubkey_bitstring.asOctets()
    return pubkey_bytes.hex().upper()


class SM2UtilsSnowland:
    """SM2加密工具类，使用snowland-smx库实现，参考Java SM2Utils.java"""
    
    @staticmethod
    def encrypt_by_public_key(data: str, public_key_b64: str) -> Optional[str]:
        """
        使用SM2公钥加密数据
        
        Args:
            data: 需要加密的字符串数据
            public_key_b64: Base64编码的X.509 DER格式公钥
            
        Returns:
            加密后的十六进制字符串（大写），与Java Hutool的encryptBcd格式一致
            失败时返回None
        """
        if not SNOWLAND_SMX_AVAILABLE:
            logger.error("snowland-smx库未安装，无法进行加密")
            return None
            
        try:
            # Step 1: Base64 解码 DER 数据
            der_data = base64.b64decode(public_key_b64.strip())
            
            # Step 2: 提取原始 SM2 公钥 Hex（x + y）
            raw_pubkey_hex = extract_raw_pubkey_from_x509_der(der_data)
            
            # Step 3: 使用 snowland-smx 加密
            # snowland-smx的Encrypt函数参数: Encrypt(data, public_key, len_para, is_hex)
            # len_para = 64 表示使用64字节长度参数
            # is_hex = 0 表示data不是十六进制字符串
            encrypted_bytes = Encrypt(data.encode('utf-8'), raw_pubkey_hex, 64, 0)
            
            if encrypted_bytes is None:
                logger.error("snowland-smx加密返回None")
                return None
                
            # Step 4: 返回十六进制编码结果（大写）
            return encrypted_bytes.hex().upper()
            
        except Exception as e:
            logger.error(f"加密数据发生异常: {e}")
            return None
    
    @staticmethod
    def str_to_public_key(public_key_str: str) -> Optional[str]:
        """
        从Base64字符串中提取原始公钥（兼容性方法）
        
        Args:
            public_key_str: Base64编码的公钥字符串
            
        Returns:
            原始公钥的十六进制字符串，失败时返回None
        """
        try:
            der_data = base64.b64decode(public_key_str.strip())
            return extract_raw_pubkey_from_x509_der(der_data)
        except Exception as e:
            logger.error(f"解析公钥失败: {e}")
            return None


# 为了兼容性，提供与原有代码相同的函数接口
def encrypt_by_public_key(data: str, public_key_b64: str) -> Optional[str]:
    """
    兼容性函数：使用SM2公钥加密数据
    """
    return SM2UtilsSnowland.encrypt_by_public_key(data, public_key_b64)


def str_to_public_key(public_key_str: str) -> Optional[str]:
    """
    兼容性函数：从字符串中提取公钥
    """
    return SM2UtilsSnowland.str_to_public_key(public_key_str)


# 测试函数
def test_snowland_smx():
    """测试snowland-smx库的基本功能"""
    print("=== 测试snowland-smx库 ===")
    
    if not SNOWLAND_SMX_AVAILABLE:
        print("❌ snowland-smx库未安装")
        return False
    
    try:
        # 测试密钥生成
        print("1. 测试密钥生成...")
        pk, sk = generate_keypair()
        print(f"✅ 密钥生成成功")
        print(f"公钥长度: {len(pk)}")
        print(f"私钥长度: {len(sk)}")
        
        # 测试加密
        print("\n2. 测试加密...")
        test_data = "Hello snowland-smx"
        encrypted = Encrypt(test_data.encode('utf-8'), pk, 64, 0)
        
        if encrypted:
            print(f"✅ 加密成功")
            print(f"加密结果长度: {len(encrypted)} bytes")
            print(f"加密结果(hex): {encrypted.hex()[:100]}...")
            return True
        else:
            print("❌ 加密失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


if __name__ == "__main__":
    # 运行测试
    test_snowland_smx()
